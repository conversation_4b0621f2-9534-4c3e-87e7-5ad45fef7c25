# -*- coding: utf-8 -*-
"""
Interactive PyPOTS Quick-start Tutorial

This is an interactive version of the PyPOTS Quick-start tutorial that allows users to run
specific examples or all examples sequentially. PyPOTS supports five common analysis tasks:
imputation, forecasting, classification, clustering, and anomaly detection on time series
data even when your data contains missing values.

Original tutorial from: https://colab.research.google.com/drive/1xAlxYrEn5JS_M-jTO17gIy27OLZRvCvx

For more models, refer to: https://github.com/WenjieDu/PyPOTS/?tab=readme-ov-file#-available-algorithms
"""

import pypots
import os
import sys
import numpy as np
import benchpots
from pypots.utils.random import set_random_seed
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.metrics import confusion_matrix, roc_curve, auc, precision_recall_curve
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# Set proxy configuration
os.environ["HTTP_PROXY"] = "http://10.237.81.130:8080"
os.environ["HTTPS_PROXY"] = "http://10.237.81.130:8080"

# Global variables for datasets
physionet2012_dataset = None
ANDO_dataset = None

def print_separator(title):
    """Print a formatted separator for better output readability."""
    print("\n" + "="*80)
    print(f"  {title}")
    print("="*80 + "\n")

def setup_plotting():
    """Setup plotting configuration and create plots directory."""
    plt.style.use('default')
    sns.set_palette("husl")

    # Create plots directory if it doesn't exist
    plots_dir = "plots"
    if not os.path.exists(plots_dir):
        os.makedirs(plots_dir)
        print(f"Created plots directory: {plots_dir}")

    return plots_dir

def save_and_show_plot(filename, title="", show_plot=True):
    """Save plot to file and optionally display it."""
    plots_dir = setup_plotting()
    filepath = os.path.join(plots_dir, filename)

    plt.tight_layout()
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    print(f"📊 Plot saved: {filepath}")

    if show_plot:
        plt.show()
    else:
        plt.close()

def plot_time_series_sample(data, title, filename, n_samples=5, missing_mask=None,
                           imputed_data=None, predictions=None, anomalies=None):
    """Plot sample time series data with various overlays."""
    fig, axes = plt.subplots(n_samples, 1, figsize=(12, 2*n_samples))
    if n_samples == 1:
        axes = [axes]

    for i in range(min(n_samples, data.shape[0])):
        ax = axes[i]

        # Plot original data
        ax.plot(data[i], label='Original Data', alpha=0.7, linewidth=1.5)

        # Plot missing values if mask provided
        if missing_mask is not None:
            missing_indices = np.where(missing_mask[i])[0]
            ax.scatter(missing_indices, data[i][missing_indices],
                      color='red', s=20, label='Missing Values', zorder=5)

        # Plot imputed data if provided
        if imputed_data is not None:
            ax.plot(imputed_data[i], label='Imputed Data',
                   linestyle='--', alpha=0.8, linewidth=1.5)

        # Plot predictions if provided
        if predictions is not None:
            pred_start = data.shape[1] if len(predictions.shape) == 2 else data.shape[1]
            pred_indices = np.arange(pred_start, pred_start + predictions.shape[-1])
            if len(predictions.shape) == 3:  # Multiple samples
                ax.plot(pred_indices, predictions[i].mean(axis=0),
                       label='Prediction', color='green', linewidth=2)
            else:
                ax.plot(pred_indices, predictions[i],
                       label='Prediction', color='green', linewidth=2)

        # Highlight anomalies if provided
        if anomalies is not None:
            anomaly_indices = np.where(anomalies[i])[0]
            ax.scatter(anomaly_indices, data[i][anomaly_indices],
                      color='red', s=30, marker='x', label='Anomalies', zorder=5)

        ax.set_title(f'Sample {i+1}')
        ax.set_xlabel('Time Steps')
        ax.set_ylabel('Value')
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.suptitle(title, fontsize=14, fontweight='bold')
    save_and_show_plot(filename, title, show_plot=False)

def plot_imputation_results(original_data, imputed_data, missing_mask, ground_truth,
                           model_name, mae_score):
    """Plot comprehensive imputation results."""
    print_separator(f"📊 Generating {model_name} Visualization Plots")

    # 1. Before/After Comparison
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Sample time series comparison
    sample_idx = 0
    axes[0, 0].plot(original_data[sample_idx], label='Original (with missing)', alpha=0.7)
    axes[0, 0].plot(imputed_data[sample_idx], label='Imputed', alpha=0.8, linestyle='--')
    missing_indices = np.where(missing_mask[sample_idx])[0]
    axes[0, 0].scatter(missing_indices, imputed_data[sample_idx][missing_indices],
                      color='red', s=30, label='Imputed values', zorder=5)
    axes[0, 0].set_title('Sample Time Series: Before vs After Imputation')
    axes[0, 0].set_xlabel('Time Steps')
    axes[0, 0].set_ylabel('Value')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. Imputation Error Heatmap
    error_matrix = np.abs(imputed_data - ground_truth) * missing_mask
    im = axes[0, 1].imshow(error_matrix[:20], aspect='auto', cmap='Reds')
    axes[0, 1].set_title('Imputation Error Heatmap (First 20 samples)')
    axes[0, 1].set_xlabel('Time Steps')
    axes[0, 1].set_ylabel('Samples')
    plt.colorbar(im, ax=axes[0, 1], label='Absolute Error')

    # 3. Error Distribution
    imputation_errors = np.abs(imputed_data - ground_truth)[missing_mask]
    axes[1, 0].hist(imputation_errors, bins=50, alpha=0.7, edgecolor='black')
    axes[1, 0].axvline(np.mean(imputation_errors), color='red', linestyle='--',
                      label=f'Mean Error: {np.mean(imputation_errors):.4f}')
    axes[1, 0].set_title('Distribution of Imputation Errors')
    axes[1, 0].set_xlabel('Absolute Error')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Scatter plot: True vs Imputed values
    true_values = ground_truth[missing_mask]
    imputed_values = imputed_data[missing_mask]
    axes[1, 1].scatter(true_values, imputed_values, alpha=0.6, s=10)
    min_val, max_val = min(true_values.min(), imputed_values.min()), max(true_values.max(), imputed_values.max())
    axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'r--', label='Perfect Imputation')
    axes[1, 1].set_title(f'True vs Imputed Values (MAE: {mae_score:.4f})')
    axes[1, 1].set_xlabel('True Values')
    axes[1, 1].set_ylabel('Imputed Values')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.suptitle(f'{model_name} Imputation Results', fontsize=16, fontweight='bold')
    save_and_show_plot(f'{model_name.lower()}_imputation_results.png')

def plot_forecasting_results(historical_data, true_future, predictions, model_name, mae_score):
    """Plot comprehensive forecasting results."""
    print_separator(f"📊 Generating {model_name} Visualization Plots")

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. Sample forecasting visualization
    sample_idx = 0
    hist_len = historical_data.shape[1]
    total_len = hist_len + true_future.shape[1]

    # Plot historical data
    axes[0, 0].plot(range(hist_len), historical_data[sample_idx],
                   label='Historical Data', color='blue', linewidth=2)

    # Plot true future
    future_indices = range(hist_len, total_len)
    axes[0, 0].plot(future_indices, true_future[sample_idx],
                   label='True Future', color='green', linewidth=2)

    # Plot predictions
    if len(predictions.shape) == 3:  # Multiple samples (e.g., CSDI)
        pred_mean = predictions[sample_idx].mean(axis=0)
        pred_std = predictions[sample_idx].std(axis=0)
        axes[0, 0].plot(future_indices, pred_mean,
                       label='Prediction', color='red', linewidth=2, linestyle='--')
        axes[0, 0].fill_between(future_indices, pred_mean - pred_std, pred_mean + pred_std,
                               alpha=0.3, color='red', label='Prediction Uncertainty')
    else:
        axes[0, 0].plot(future_indices, predictions[sample_idx],
                       label='Prediction', color='red', linewidth=2, linestyle='--')

    axes[0, 0].axvline(x=hist_len, color='black', linestyle=':', alpha=0.7, label='Forecast Start')
    axes[0, 0].set_title('Sample Forecasting: Historical vs Predicted')
    axes[0, 0].set_xlabel('Time Steps')
    axes[0, 0].set_ylabel('Value')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. Prediction Error Distribution
    if len(predictions.shape) == 3:
        pred_for_error = predictions.mean(axis=1)
    else:
        pred_for_error = predictions

    errors = np.abs(pred_for_error - true_future).flatten()
    axes[0, 1].hist(errors, bins=50, alpha=0.7, edgecolor='black')
    axes[0, 1].axvline(np.mean(errors), color='red', linestyle='--',
                      label=f'Mean Error: {np.mean(errors):.4f}')
    axes[0, 1].set_title('Distribution of Forecasting Errors')
    axes[0, 1].set_xlabel('Absolute Error')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. True vs Predicted scatter plot
    true_flat = true_future.flatten()
    pred_flat = pred_for_error.flatten()
    axes[1, 0].scatter(true_flat, pred_flat, alpha=0.6, s=10)
    min_val, max_val = min(true_flat.min(), pred_flat.min()), max(true_flat.max(), pred_flat.max())
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', label='Perfect Prediction')
    axes[1, 0].set_title(f'True vs Predicted Values (MAE: {mae_score:.4f})')
    axes[1, 0].set_xlabel('True Values')
    axes[1, 0].set_ylabel('Predicted Values')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Error by time step
    step_errors = np.mean(np.abs(pred_for_error - true_future), axis=0)
    axes[1, 1].plot(range(1, len(step_errors) + 1), step_errors, 'o-', linewidth=2, markersize=6)
    axes[1, 1].set_title('Forecasting Error by Time Step')
    axes[1, 1].set_xlabel('Forecast Horizon')
    axes[1, 1].set_ylabel('Mean Absolute Error')
    axes[1, 1].grid(True, alpha=0.3)

    plt.suptitle(f'{model_name} Forecasting Results', fontsize=16, fontweight='bold')
    save_and_show_plot(f'{model_name.lower()}_forecasting_results.png')

def plot_classification_results(data, true_labels, predicted_probs, metrics, model_name):
    """Plot comprehensive classification results."""
    print_separator(f"📊 Generating {model_name} Visualization Plots")

    # Convert probabilities to binary predictions
    predicted_labels = (predicted_probs > 0.5).astype(int)

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. Confusion Matrix
    cm = confusion_matrix(true_labels, predicted_labels)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, 0])
    axes[0, 0].set_title('Confusion Matrix')
    axes[0, 0].set_xlabel('Predicted')
    axes[0, 0].set_ylabel('Actual')

    # 2. ROC Curve
    fpr, tpr, _ = roc_curve(true_labels, predicted_probs)
    roc_auc = auc(fpr, tpr)
    axes[0, 1].plot(fpr, tpr, color='darkorange', lw=2,
                   label=f'ROC curve (AUC = {roc_auc:.4f})')
    axes[0, 1].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
    axes[0, 1].set_xlim([0.0, 1.0])
    axes[0, 1].set_ylim([0.0, 1.05])
    axes[0, 1].set_xlabel('False Positive Rate')
    axes[0, 1].set_ylabel('True Positive Rate')
    axes[0, 1].set_title('ROC Curve')
    axes[0, 1].legend(loc="lower right")
    axes[0, 1].grid(True, alpha=0.3)

    # 3. Precision-Recall Curve
    precision, recall, _ = precision_recall_curve(true_labels, predicted_probs)
    pr_auc = auc(recall, precision)
    axes[1, 0].plot(recall, precision, color='blue', lw=2,
                   label=f'PR curve (AUC = {pr_auc:.4f})')
    axes[1, 0].set_xlabel('Recall')
    axes[1, 0].set_ylabel('Precision')
    axes[1, 0].set_title('Precision-Recall Curve')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Sample time series colored by class
    sample_indices = np.random.choice(len(data), min(5, len(data)), replace=False)
    colors = ['blue' if label == 0 else 'red' for label in true_labels[sample_indices]]
    pred_colors = ['lightblue' if pred == 0 else 'lightcoral' for pred in predicted_labels[sample_indices]]

    for i, idx in enumerate(sample_indices):
        offset = i * 2  # Offset for visualization
        axes[1, 1].plot(data[idx] + offset, color=colors[i], alpha=0.7,
                       label=f'True Class {true_labels[idx]}' if i < 2 else "")
        axes[1, 1].plot(data[idx] + offset, color=pred_colors[i], alpha=0.3, linewidth=3,
                       label=f'Pred Class {predicted_labels[idx]}' if i < 2 else "")

    axes[1, 1].set_title('Sample Time Series by Class')
    axes[1, 1].set_xlabel('Time Steps')
    axes[1, 1].set_ylabel('Value (with offset)')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    # Add metrics text
    metrics_text = f"ROC AUC: {metrics['roc_auc']:.4f}\nPR AUC: {metrics['pr_auc']:.4f}\n"
    metrics_text += f"F1: {metrics['f1']:.4f}\nPrecision: {metrics['precision']:.4f}\nRecall: {metrics['recall']:.4f}"
    fig.text(0.02, 0.02, metrics_text, fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

    plt.suptitle(f'{model_name} Classification Results', fontsize=16, fontweight='bold')
    save_and_show_plot(f'{model_name.lower()}_classification_results.png')

def plot_clustering_results(data, cluster_labels, true_labels, metrics, model_name):
    """Plot comprehensive clustering results."""
    print_separator(f"📊 Generating {model_name} Visualization Plots")

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. t-SNE visualization of clusters
    # Use a subset for t-SNE if data is too large
    n_samples = min(1000, len(data))
    indices = np.random.choice(len(data), n_samples, replace=False)

    # Flatten time series for t-SNE
    data_flat = data[indices].reshape(n_samples, -1)
    tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, n_samples//4))
    data_tsne = tsne.fit_transform(data_flat)

    scatter = axes[0, 0].scatter(data_tsne[:, 0], data_tsne[:, 1],
                                c=cluster_labels[indices], cmap='tab10', alpha=0.7)
    axes[0, 0].set_title('t-SNE Visualization of Clusters')
    axes[0, 0].set_xlabel('t-SNE 1')
    axes[0, 0].set_ylabel('t-SNE 2')
    plt.colorbar(scatter, ax=axes[0, 0], label='Cluster')

    # 2. Cluster distribution
    unique_clusters, counts = np.unique(cluster_labels, return_counts=True)
    axes[0, 1].bar(unique_clusters, counts, alpha=0.7)
    axes[0, 1].set_title('Cluster Size Distribution')
    axes[0, 1].set_xlabel('Cluster ID')
    axes[0, 1].set_ylabel('Number of Samples')
    axes[0, 1].grid(True, alpha=0.3)

    # 3. Representative time series for each cluster
    n_clusters = len(unique_clusters)
    colors = plt.cm.tab10(np.linspace(0, 1, n_clusters))

    for cluster_id in unique_clusters:
        cluster_mask = cluster_labels == cluster_id
        cluster_data = data[cluster_mask]

        # Compute cluster centroid
        centroid = np.mean(cluster_data, axis=0)
        axes[1, 0].plot(centroid, color=colors[cluster_id],
                       label=f'Cluster {cluster_id}', linewidth=2)

    axes[1, 0].set_title('Cluster Centroids')
    axes[1, 0].set_xlabel('Time Steps')
    axes[1, 0].set_ylabel('Value')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Confusion matrix between predicted and true clusters (if available)
    if true_labels is not None:
        # Create confusion matrix
        cm = confusion_matrix(true_labels, cluster_labels)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, 1])
        axes[1, 1].set_title('Clustering vs True Labels')
        axes[1, 1].set_xlabel('Predicted Cluster')
        axes[1, 1].set_ylabel('True Label')
    else:
        # Show sample time series from different clusters
        for cluster_id in unique_clusters[:min(5, n_clusters)]:
            cluster_mask = cluster_labels == cluster_id
            cluster_indices = np.where(cluster_mask)[0]
            if len(cluster_indices) > 0:
                sample_idx = cluster_indices[0]
                axes[1, 1].plot(data[sample_idx] + cluster_id * 2,
                               color=colors[cluster_id],
                               label=f'Cluster {cluster_id}', alpha=0.7)

        axes[1, 1].set_title('Sample Time Series by Cluster')
        axes[1, 1].set_xlabel('Time Steps')
        axes[1, 1].set_ylabel('Value (with offset)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

    # Add metrics text
    metrics_text = f"Rand Index: {metrics['RI']:.4f}\nCluster Purity: {metrics['CP']:.4f}"
    fig.text(0.02, 0.02, metrics_text, fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

    plt.suptitle(f'{model_name} Clustering Results', fontsize=16, fontweight='bold')
    save_and_show_plot(f'{model_name.lower()}_clustering_results.png')

def plot_anomaly_detection_results(data, true_anomalies, predicted_anomalies,
                                  anomaly_scores, metrics, model_name):
    """Plot comprehensive anomaly detection results."""
    print_separator(f"📊 Generating {model_name} Visualization Plots")

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. Sample time series with anomalies highlighted
    sample_idx = 0
    time_steps = range(len(data[sample_idx]))

    axes[0, 0].plot(time_steps, data[sample_idx], 'b-', alpha=0.7, label='Normal Data')

    # Highlight true anomalies
    true_anomaly_indices = np.where(true_anomalies[sample_idx])[0]
    if len(true_anomaly_indices) > 0:
        axes[0, 0].scatter(true_anomaly_indices, data[sample_idx][true_anomaly_indices],
                          color='red', s=50, marker='o', label='True Anomalies', zorder=5)

    # Highlight predicted anomalies
    pred_anomaly_indices = np.where(predicted_anomalies[sample_idx])[0]
    if len(pred_anomaly_indices) > 0:
        axes[0, 0].scatter(pred_anomaly_indices, data[sample_idx][pred_anomaly_indices],
                          color='orange', s=30, marker='x', label='Predicted Anomalies', zorder=4)

    axes[0, 0].set_title('Sample Time Series with Anomaly Detection')
    axes[0, 0].set_xlabel('Time Steps')
    axes[0, 0].set_ylabel('Value')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. Anomaly score distribution
    normal_scores = anomaly_scores[true_anomalies == 0]
    anomaly_scores_true = anomaly_scores[true_anomalies == 1]

    axes[0, 1].hist(normal_scores, bins=50, alpha=0.7, label='Normal', density=True, color='blue')
    axes[0, 1].hist(anomaly_scores_true, bins=50, alpha=0.7, label='Anomaly', density=True, color='red')
    axes[0, 1].set_title('Anomaly Score Distribution')
    axes[0, 1].set_xlabel('Anomaly Score')
    axes[0, 1].set_ylabel('Density')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. ROC Curve
    fpr, tpr, thresholds = roc_curve(true_anomalies.flatten(), anomaly_scores.flatten())
    roc_auc = auc(fpr, tpr)

    axes[1, 0].plot(fpr, tpr, color='darkorange', lw=2,
                   label=f'ROC curve (AUC = {roc_auc:.4f})')
    axes[1, 0].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
    axes[1, 0].set_xlim([0.0, 1.0])
    axes[1, 0].set_ylim([0.0, 1.05])
    axes[1, 0].set_xlabel('False Positive Rate')
    axes[1, 0].set_ylabel('True Positive Rate')
    axes[1, 0].set_title('ROC Curve for Anomaly Detection')
    axes[1, 0].legend(loc="lower right")
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Precision-Recall Curve
    precision, recall, _ = precision_recall_curve(true_anomalies.flatten(), anomaly_scores.flatten())
    pr_auc = auc(recall, precision)

    axes[1, 1].plot(recall, precision, color='blue', lw=2,
                   label=f'PR curve (AUC = {pr_auc:.4f})')
    axes[1, 1].set_xlabel('Recall')
    axes[1, 1].set_ylabel('Precision')
    axes[1, 1].set_title('Precision-Recall Curve')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    # Add metrics text
    metrics_text = f"Accuracy: {metrics['accuracy']:.4f}\nF1: {metrics['f1']:.4f}\n"
    metrics_text += f"Precision: {metrics['precision']:.4f}\nRecall: {metrics['recall']:.4f}"
    fig.text(0.02, 0.02, metrics_text, fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

    plt.suptitle(f'{model_name} Anomaly Detection Results', fontsize=16, fontweight='bold')
    save_and_show_plot(f'{model_name.lower()}_anomaly_detection_results.png')

def prepare_physionet2012_dataset():
    """Prepare the PhysioNet-2012 dataset for tutorial examples."""
    global physionet2012_dataset

    if physionet2012_dataset is None:
        print_separator("📀 Preparing PhysioNet-2012 Dataset")
        set_random_seed()

        # Load the PhysioNet-2012 dataset
        physionet2012_dataset = benchpots.datasets.preprocess_physionet2012(
            subset="set-a",
            rate=0.1,  # the rate of missing values artificially created to evaluate algorithms
        )

        print("Dataset keys:", physionet2012_dataset.keys())
        print("Dataset prepared successfully!")

    return physionet2012_dataset

def prepare_anomaly_detection_dataset():
    """Prepare synthetic dataset for anomaly detection examples."""
    global ANDO_dataset

    if ANDO_dataset is None:
        print_separator("📀 Preparing Synthetic Dataset for Anomaly Detection")

        from benchpots.datasets import preprocess_random_walk

        N_STEPS = 6
        N_PRED_STEPS = 2
        N_FEATURES = 5
        ANOMALY_RATE = 0.05
        MISSING_RATE = 0.1

        ANDO_dataset = preprocess_random_walk(
            n_steps=N_STEPS + N_PRED_STEPS,  # the total sequence length
            n_features=N_FEATURES,
            anomaly_rate=ANOMALY_RATE,
            missing_rate=MISSING_RATE,
        )

        # Store parameters for later use
        ANDO_dataset['N_STEPS'] = N_STEPS
        ANDO_dataset['N_PRED_STEPS'] = N_PRED_STEPS
        ANDO_dataset['N_FEATURES'] = N_FEATURES
        ANDO_dataset['ANOMALY_RATE'] = ANOMALY_RATE
        ANDO_dataset['MISSING_RATE'] = MISSING_RATE

        print("Anomaly detection dataset prepared successfully!")

    return ANDO_dataset

def prepare_imputation_datasets():
    """Prepare datasets for imputation tasks."""
    dataset = prepare_physionet2012_dataset()

    # assemble the datasets for training
    dataset_for_IMPU_training = {
        "X": dataset['train_X'],
    }
    # assemble the datasets for validation
    dataset_for_IMPU_validating = {
        "X": dataset['val_X'],
        "X_ori": dataset['val_X_ori'],
    }
    # assemble the datasets for test
    dataset_for_IMPU_testing = {
        "X": dataset['test_X'],
    }
    ## calculate the mask to indicate the ground truth positions in test_X_ori, will be used by metric funcs to evaluate models
    test_X_indicating_mask = np.isnan(dataset['test_X_ori']) ^ np.isnan(dataset['test_X'])
    test_X_ori = np.nan_to_num(dataset['test_X_ori'])  # metric functions do not accpet input with NaNs, hence fill NaNs with 0

    return dataset_for_IMPU_training, dataset_for_IMPU_validating, dataset_for_IMPU_testing, test_X_indicating_mask, test_X_ori

def run_saits_imputation():
    """Run SAITS imputation example."""
    print_separator("🚀 SAITS Imputation Example")

    from pypots.nn.functional import calc_mae
    from pypots.optim import Adam
    from pypots.imputation import SAITS

    # Prepare datasets
    dataset_for_IMPU_training, dataset_for_IMPU_validating, dataset_for_IMPU_testing, test_X_indicating_mask, test_X_ori = prepare_imputation_datasets()
    dataset = prepare_physionet2012_dataset()

    # initialize the model
    saits = SAITS(
        n_steps=dataset['n_steps'],
        n_features=dataset['n_features'],
        n_layers=1,
        d_model=256,
        d_ffn=128,
        n_heads=4,
        d_k=64,
        d_v=64,
        dropout=0.1,
        ORT_weight=1,  # you can adjust the weight values of arguments ORT_weight
        # and MIT_weight to make the SAITS model focus more on one task. Usually you can just leave them to the default values, i.e. 1.
        MIT_weight=1,
        batch_size=32,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/imputation/saits",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training SAITS model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    saits.fit(
        train_set = dataset_for_IMPU_training,
        val_set=dataset_for_IMPU_validating
    )

    print("Running inference...")
    # the testing stage, impute the originally-missing values and artificially-missing values in the test set
    saits_results = saits.predict(dataset_for_IMPU_testing)
    saits_imputation = saits_results["imputation"]

    # calculate mean absolute error on the ground truth (artificially-missing values)
    testing_mae = calc_mae(
        saits_imputation,
        test_X_ori,
        test_X_indicating_mask,
    )
    print(f"✅ SAITS Testing mean absolute error: {testing_mae:.4f}")

    # Generate visualization plots
    plot_imputation_results(
        original_data=dataset_for_IMPU_testing["X"],
        imputed_data=saits_imputation,
        missing_mask=test_X_indicating_mask,
        ground_truth=test_X_ori,
        model_name="SAITS",
        mae_score=testing_mae
    )

    print("SAITS imputation example completed successfully!")



def run_csdi_imputation():
    """Run CSDI imputation example."""
    print_separator("🚀 CSDI Imputation Example")

    from pypots.optim import Adam
    from pypots.imputation import CSDI
    from pypots.nn.functional import calc_mae

    # Prepare datasets
    dataset_for_IMPU_training, dataset_for_IMPU_validating, dataset_for_IMPU_testing, test_X_indicating_mask, test_X_ori = prepare_imputation_datasets()
    dataset = prepare_physionet2012_dataset()

    # initialize the model
    csdi = CSDI(
        n_steps=dataset['n_steps'],
        n_features=dataset['n_features'],
        n_layers=3,
        n_heads=2,
        n_channels=128,
        d_time_embedding=64,
        d_feature_embedding=32,
        d_diffusion_embedding=128,
        target_strategy="random",
        n_diffusion_steps=50,
        batch_size=32,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/imputation/csdi",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training CSDI model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    csdi.fit(
        train_set = dataset_for_IMPU_training,
        val_set = dataset_for_IMPU_validating,
    )

    print("Running inference...")
    # the testing stage, impute the originally-missing values and artificially-missing values in the test set
    csdi_results = csdi.predict(
        dataset_for_IMPU_testing,
        n_sampling_times = 2,  # CSDI has an argument to control the number of sampling times during inference
    )
    csdi_imputation = csdi_results["imputation"]

    print(f"The shape of csdi_imputation is {csdi_imputation.shape}")

    # for error calculation, we need to take the mean value of the multiple samplings for each data sample
    mean_csdi_imputation = csdi_imputation.mean(axis=1)

    # calculate mean absolute error on the ground truth (artificially-missing values)
    testing_mae = calc_mae(
        mean_csdi_imputation,
        test_X_ori,
        test_X_indicating_mask,
    )
    print(f"✅ CSDI Testing mean absolute error: {testing_mae:.4f}")

    # Generate visualization plots
    plot_imputation_results(
        original_data=dataset_for_IMPU_testing["X"],
        imputed_data=mean_csdi_imputation,
        missing_mask=test_X_indicating_mask,
        ground_truth=test_X_ori,
        model_name="CSDI",
        mae_score=testing_mae
    )

    print("CSDI imputation example completed successfully!")

def prepare_forecasting_datasets():
    """Prepare datasets for forecasting tasks."""
    dataset = prepare_physionet2012_dataset()

    N_PRED_STEPS = 6

    dataset_for_FORE_training = {
        "X": dataset['train_X'][:, :-N_PRED_STEPS],
        "X_pred": dataset['train_X'][:, -N_PRED_STEPS:],
    }

    dataset_for_FORE_validating = {
        "X": dataset['val_X'][:, :-N_PRED_STEPS],
        "X_pred": dataset['val_X_ori'][:, -N_PRED_STEPS:],
    }

    dataset_for_FORE_testing = {
        "X": dataset['test_X'][:, :-N_PRED_STEPS],  # we only take the first 42 steps for model input,
        # and let the model forecast the left 6 steps
    }

    return dataset_for_FORE_training, dataset_for_FORE_validating, dataset_for_FORE_testing, N_PRED_STEPS

def run_tefn_forecasting():
    """Run TEFN forecasting example."""
    print_separator("🚀 TEFN Forecasting Example")

    from pypots.optim import Adam
    from pypots.forecasting import TEFN
    from pypots.nn.functional import calc_mae

    # Prepare datasets
    dataset_for_FORE_training, dataset_for_FORE_validating, dataset_for_FORE_testing, N_PRED_STEPS = prepare_forecasting_datasets()
    dataset = prepare_physionet2012_dataset()

    # initialize the model
    tefn = TEFN(
        n_steps = dataset["n_steps"] - N_PRED_STEPS,
        n_features = dataset["n_features"],
        n_pred_steps = N_PRED_STEPS,
        n_pred_features = dataset["n_features"],
        n_fod = 2,
        batch_size=32,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/forecasting/tefn",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training TEFN model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    tefn.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)

    print("Running inference...")
    # the testing stage
    tefn_results = tefn.predict(dataset_for_FORE_testing)
    tefn_prediction = tefn_results["forecasting"]

    # calculate the mean absolute error on the ground truth in the forecasting task
    testing_mae = calc_mae(
        tefn_prediction,
        np.nan_to_num(dataset['test_X'][:, -N_PRED_STEPS:]),
        (~np.isnan(dataset['test_X'][:, -N_PRED_STEPS:])).astype(int),
    )
    print(f"✅ TEFN Testing mean absolute error: {testing_mae:.4f}")

    # Generate visualization plots
    plot_forecasting_results(
        historical_data=dataset_for_FORE_testing["X"],
        true_future=np.nan_to_num(dataset['test_X'][:, -N_PRED_STEPS:]),
        predictions=tefn_prediction,
        model_name="TEFN",
        mae_score=testing_mae
    )

    print("TEFN forecasting example completed successfully!")

def run_timemixer_forecasting():
    """Run TimeMixer++ forecasting example."""
    print_separator("🚀 TimeMixer++ Forecasting Example")

    from pypots.optim import Adam
    from pypots.forecasting import TimeMixer
    from pypots.nn.functional import calc_mae

    # Prepare datasets
    dataset_for_FORE_training, dataset_for_FORE_validating, dataset_for_FORE_testing, N_PRED_STEPS = prepare_forecasting_datasets()
    dataset = prepare_physionet2012_dataset()

    timemixer = TimeMixer(
        n_steps = dataset["n_steps"] - N_PRED_STEPS,
        n_features = dataset["n_features"],
        n_pred_steps = N_PRED_STEPS,
        n_pred_features = dataset["n_features"],
        term = "short",
        n_layers=2,
        top_k=5,
        d_model=32,
        d_ffn=32,
        moving_avg=25,
        downsampling_window=2,
        downsampling_layers=1,
        use_norm=True,
        dropout=0.1,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/forecasting/timemixer",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training TimeMixer model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    timemixer.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)

    print("Running inference...")
    # the testing stage
    timemixer_results = timemixer.predict(dataset_for_FORE_testing)
    timemixer_prediction = timemixer_results["forecasting"]

    # calculate the mean absolute error on the ground truth in the forecasting task
    testing_mae = calc_mae(
        timemixer_prediction,
        np.nan_to_num(dataset['test_X'][:, -N_PRED_STEPS:]),
        (~np.isnan(dataset['test_X'][:, -N_PRED_STEPS:])).astype(int),
    )
    print(f"✅ TimeMixer Testing mean absolute error: {testing_mae:.4f}")

    # Generate visualization plots
    plot_forecasting_results(
        historical_data=dataset_for_FORE_testing["X"],
        true_future=np.nan_to_num(dataset['test_X'][:, -N_PRED_STEPS:]),
        predictions=timemixer_prediction,
        model_name="TimeMixer",
        mae_score=testing_mae
    )

    print("TimeMixer forecasting example completed successfully!")

def prepare_classification_datasets():
    """Prepare datasets for classification tasks."""
    dataset = prepare_physionet2012_dataset()

    dataset_for_CLAS_training = {
        "X": dataset['train_X'],
        "y": dataset['train_y'],
    }

    dataset_for_CLAS_validating = {
        "X": dataset['val_X'],
        "y": dataset['val_y'],
    }

    dataset_for_CLAS_testing = {
        "X": dataset['test_X'],
        "y": dataset['test_y'],
    }

    return dataset_for_CLAS_training, dataset_for_CLAS_validating, dataset_for_CLAS_testing

def run_timesnet_classification():
    """Run TimesNet classification example."""
    print_separator("🚀 TimesNet Classification Example")

    from pypots.optim import Adam
    from pypots.classification import TimesNet
    from pypots.utils.metrics import calc_binary_classification_metrics

    # Prepare datasets
    dataset_for_CLAS_training, dataset_for_CLAS_validating, dataset_for_CLAS_testing = prepare_classification_datasets()
    dataset = prepare_physionet2012_dataset()

    # initialize the model
    timesnet = TimesNet(
        n_steps=dataset['n_steps'],
        n_features=dataset['n_features'],
        n_classes=dataset["n_classes"],
        n_layers=2,
        top_k=3,
        d_model=32,
        d_ffn=32,
        n_kernels=3,
        dropout=0.1,
        batch_size=32,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/classification/timesnet",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training TimesNet model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    timesnet.fit(
        train_set = dataset_for_CLAS_training,
        val_set = dataset_for_CLAS_validating,
    )

    print("Running inference...")
    # the testing stage
    timesnet_results = timesnet.predict(dataset_for_CLAS_testing)
    timesnet_prediction = timesnet_results["classification"]

    # calculate the values of binary classification metrics on the model's prediction
    metrics = calc_binary_classification_metrics(timesnet_prediction, dataset_for_CLAS_testing["y"])
    print("✅ TimesNet Testing classification metrics:")
    print(f'   ROC_AUC: {metrics["roc_auc"]:.4f}')
    print(f'   PR_AUC: {metrics["pr_auc"]:.4f}')
    print(f'   F1: {metrics["f1"]:.4f}')
    print(f'   Precision: {metrics["precision"]:.4f}')
    print(f'   Recall: {metrics["recall"]:.4f}')

    # Generate visualization plots
    plot_classification_results(
        data=dataset_for_CLAS_testing["X"],
        true_labels=dataset_for_CLAS_testing["y"],
        predicted_probs=timesnet_prediction,
        metrics=metrics,
        model_name="TimesNet"
    )

    print("TimesNet classification example completed successfully!")

def run_brits_classification():
    """Run BRITS classification example."""
    print_separator("🚀 BRITS Classification Example")

    from pypots.optim import Adam
    from pypots.classification import BRITS
    from pypots.utils.metrics import calc_binary_classification_metrics

    # Prepare datasets
    dataset_for_CLAS_training, dataset_for_CLAS_validating, dataset_for_CLAS_testing = prepare_classification_datasets()
    dataset = prepare_physionet2012_dataset()

    # initialize the model
    brits = BRITS(
        n_steps=dataset['n_steps'],
        n_features=dataset['n_features'],
        n_classes=dataset["n_classes"],
        rnn_hidden_size=256,
        batch_size=32,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/classification/brits",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training BRITS model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    brits.fit(
        train_set = dataset_for_CLAS_training,
        val_set = dataset_for_CLAS_validating,
    )

    print("Running inference...")
    # the testing stage
    brits_results = brits.predict(dataset_for_CLAS_testing)
    brits_prediction = brits_results["classification"]

    # calculate the values of binary classification metrics on the model's prediction
    metrics = calc_binary_classification_metrics(brits_prediction, dataset_for_CLAS_testing["y"])
    print("✅ BRITS Testing classification metrics:")
    print(f'   ROC_AUC: {metrics["roc_auc"]:.4f}')
    print(f'   PR_AUC: {metrics["pr_auc"]:.4f}')
    print(f'   F1: {metrics["f1"]:.4f}')
    print(f'   Precision: {metrics["precision"]:.4f}')
    print(f'   Recall: {metrics["recall"]:.4f}')

    # Generate visualization plots
    plot_classification_results(
        data=dataset_for_CLAS_testing["X"],
        true_labels=dataset_for_CLAS_testing["y"],
        predicted_probs=brits_prediction,
        metrics=metrics,
        model_name="BRITS"
    )

    print("BRITS classification example completed successfully!")

def prepare_clustering_datasets():
    """Prepare datasets for clustering tasks."""
    dataset = prepare_physionet2012_dataset()

    # don't need validation set for clustering
    dataset_for_CLUS_training = {
        "X": np.concatenate([dataset['train_X'], dataset['val_X']], axis=0),
        "y": np.concatenate([dataset['train_y'], dataset['val_y']], axis=0),
    }

    dataset_for_CLUS_testing = {
        "X": dataset['test_X'],
        "y": dataset['test_y'],
    }

    return dataset_for_CLUS_training, dataset_for_CLUS_testing

def run_crli_clustering():
    """Run CRLI clustering example."""
    print_separator("🚀 CRLI Clustering Example")

    from pypots.optim import Adam
    from pypots.clustering import CRLI
    from pypots.utils.metrics import calc_rand_index, calc_cluster_purity

    # Prepare datasets
    dataset_for_CLUS_training, dataset_for_CLUS_testing = prepare_clustering_datasets()
    dataset = prepare_physionet2012_dataset()

    # initialize the model
    crli = CRLI(
        n_steps=dataset["n_steps"],
        n_features=dataset["n_features"],
        n_clusters=dataset["n_classes"],
        n_generator_layers=2,
        rnn_hidden_size=256,
        rnn_cell_type="GRU",
        decoder_fcn_output_dims=[256, 128],  # the output dimensions of layers in the decoder FCN.
        # Here means there are 3 layers. Leave it to default as None will results in
        # the FCN haveing only one layer.
        batch_size=32,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        G_optimizer=Adam(lr=1e-3),
        D_optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/clustering/crli",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training CRLI model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    crli.fit(train_set=dataset_for_CLUS_training)

    print("Running inference...")
    # the testing stage
    crli_results = crli.predict(dataset_for_CLUS_testing)
    crli_prediction = crli_results["clustering"]

    # calculate the values of clustering metrics on the model's prediction
    RI = calc_rand_index(crli_prediction, dataset_for_CLUS_testing["y"])
    CP = calc_cluster_purity(crli_prediction, dataset_for_CLUS_testing["y"])

    print("✅ CRLI Testing clustering metrics:")
    print(f'   RI: {RI:.4f}')
    print(f'   CP: {CP:.4f}')

    # Generate visualization plots
    metrics_dict = {"RI": RI, "CP": CP}
    plot_clustering_results(
        data=dataset_for_CLUS_testing["X"],
        cluster_labels=crli_prediction,
        true_labels=dataset_for_CLUS_testing["y"],
        metrics=metrics_dict,
        model_name="CRLI"
    )

    print("CRLI clustering example completed successfully!")

def run_vader_clustering():
    """Run VaDER clustering example."""
    print_separator("🚀 VaDER Clustering Example")

    from pypots.optim import Adam
    from pypots.clustering import VaDER
    from pypots.utils.metrics import calc_rand_index, calc_cluster_purity

    # Prepare datasets
    dataset_for_CLUS_training, dataset_for_CLUS_testing = prepare_clustering_datasets()
    dataset = prepare_physionet2012_dataset()

    # initialize the model
    vader = VaDER(
        n_steps=dataset["n_steps"],
        n_features=dataset["n_features"],
        n_clusters=dataset["n_classes"],
        rnn_hidden_size=128,
        d_mu_stddev=2,
        pretrain_epochs=20,
        batch_size=32,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/clustering/vader",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training VaDER model...")
    # train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
    vader.fit(train_set=dataset_for_CLUS_training)

    print("Running inference...")
    # the testing stage
    vader_results = vader.predict(dataset_for_CLUS_testing)
    vader_prediction = vader_results["clustering"]

    # calculate the values of clustering metrics on the model's prediction
    RI = calc_rand_index(vader_prediction, dataset_for_CLUS_testing["y"])
    CP = calc_cluster_purity(vader_prediction, dataset_for_CLUS_testing["y"])

    print("✅ VaDER Testing clustering metrics:")
    print(f'   RI: {RI:.4f}')
    print(f'   CP: {CP:.4f}')

    # Generate visualization plots
    metrics_dict = {"RI": RI, "CP": CP}
    plot_clustering_results(
        data=dataset_for_CLUS_testing["X"],
        cluster_labels=vader_prediction,
        true_labels=dataset_for_CLUS_testing["y"],
        metrics=metrics_dict,
        model_name="VaDER"
    )

    print("VaDER clustering example completed successfully!")

def prepare_anomaly_detection_datasets():
    """Prepare datasets for anomaly detection tasks."""
    dataset = prepare_anomaly_detection_dataset()

    dataset_for_ANOD_training = {
        "X": dataset['train_X'],
        "anomaly_y": dataset["train_anomaly_y"].astype(float),
    }

    dataset_for_ANOD_validating = {
        "X": dataset['val_X'],
        "X_ori": dataset["val_X_ori"],
        "anomaly_y": dataset["val_anomaly_y"].astype(float),
    }

    dataset_for_ANOD_testing = {
        "X": dataset['test_X'],
        "X_ori": dataset["test_X_ori"],
        "anomaly_y": dataset["test_anomaly_y"].astype(float),
    }

    return dataset_for_ANOD_training, dataset_for_ANOD_validating, dataset_for_ANOD_testing

def run_autoformer_anomaly_detection():
    """Run Autoformer anomaly detection example."""
    print_separator("🚀 Autoformer Anomaly Detection Example")

    from pypots.optim import Adam
    from pypots.anomaly_detection import Autoformer
    from pypots.nn.functional import calc_acc, calc_precision_recall_f1

    # Prepare datasets
    dataset_for_ANOD_training, dataset_for_ANOD_validating, dataset_for_ANOD_testing = prepare_anomaly_detection_datasets()
    dataset = prepare_anomaly_detection_dataset()

    autoformer = Autoformer(
        n_steps = dataset["n_steps"],
        n_features = dataset["n_features"],
        anomaly_rate = dataset['ANOMALY_RATE'],
        n_layers=2,
        n_heads=2,
        d_model=32,
        d_ffn=32,
        factor=3,
        moving_avg_window_size=3,
        dropout=0,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/anomaly_detection/autoformer",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training Autoformer model...")
    autoformer.fit(dataset_for_ANOD_training, dataset_for_ANOD_validating)

    print("Running inference...")
    anomaly_detection_results = autoformer.predict(dataset_for_ANOD_testing)
    anomaly_labels = dataset_for_ANOD_testing["anomaly_y"].flatten()
    accuracy = calc_acc(
        anomaly_detection_results["anomaly_detection"],
        anomaly_labels,
    )
    precision, recall, f1 = calc_precision_recall_f1(
        anomaly_detection_results["anomaly_detection"],
        anomaly_labels,
    )

    print(f"✅ Autoformer Results - Accuracy: {accuracy:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")

    # Generate visualization plots
    metrics_dict = {"accuracy": accuracy, "f1": f1, "precision": precision, "recall": recall}

    # For anomaly detection, we need to extract anomaly scores from the results
    # Assuming the anomaly detection results contain scores
    anomaly_scores = anomaly_detection_results.get("anomaly_scores", anomaly_detection_results["anomaly_detection"])

    plot_anomaly_detection_results(
        data=dataset_for_ANOD_testing["X"],
        true_anomalies=dataset_for_ANOD_testing["anomaly_y"],
        predicted_anomalies=anomaly_detection_results["anomaly_detection"],
        anomaly_scores=anomaly_scores,
        metrics=metrics_dict,
        model_name="Autoformer"
    )

    print("Autoformer anomaly detection example completed successfully!")

def run_patchtst_anomaly_detection():
    """Run PatchTST anomaly detection example."""
    print_separator("🚀 PatchTST Anomaly Detection Example")

    from pypots.optim import Adam
    from pypots.anomaly_detection import PatchTST
    from pypots.nn.functional import calc_acc, calc_precision_recall_f1

    # Prepare datasets
    dataset_for_ANOD_training, dataset_for_ANOD_validating, dataset_for_ANOD_testing = prepare_anomaly_detection_datasets()
    dataset = prepare_anomaly_detection_dataset()

    patchtst = PatchTST(
        n_steps = dataset["n_steps"],
        n_features = dataset["n_features"],
        anomaly_rate = dataset['ANOMALY_RATE'],
        n_layers=2,
        d_model=64,
        n_heads=2,
        d_k=16,
        d_v=16,
        d_ffn=32,
        patch_size=dataset["n_steps"],
        patch_stride=8,
        dropout=0.1,
        attn_dropout=0,
        # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
        epochs=10,
        # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
        # You can leave it to defualt as None to disable early stopping.
        patience=3,
        # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
        # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
        optimizer=Adam(lr=1e-3),
        # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
        # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
        # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
        num_workers=0,
        # just leave it to default as None, PyPOTS will automatically assign the best device for you.
        # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
        device=None,
        # set the path for saving tensorboard and trained model files
        saving_path="tutorial_results/anomaly_detection/patchtst",
        # only save the best model after training finished.
        # You can also set it as "better" to save models performing better ever during training.
        model_saving_strategy="best",
    )

    print("Training PatchTST model...")
    patchtst.fit(dataset_for_ANOD_training, dataset_for_ANOD_validating)

    print("Running inference...")
    anomaly_detection_results = patchtst.predict(dataset_for_ANOD_testing)
    anomaly_labels = dataset_for_ANOD_testing["anomaly_y"].flatten()
    accuracy = calc_acc(
        anomaly_detection_results["anomaly_detection"],
        anomaly_labels,
    )
    precision, recall, f1 = calc_precision_recall_f1(
        anomaly_detection_results["anomaly_detection"],
        anomaly_labels,
    )

    print(f"✅ PatchTST Results - Accuracy: {accuracy:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")

    # Generate visualization plots
    metrics_dict = {"accuracy": accuracy, "f1": f1, "precision": precision, "recall": recall}

    # For anomaly detection, we need to extract anomaly scores from the results
    # Assuming the anomaly detection results contain scores
    anomaly_scores = anomaly_detection_results.get("anomaly_scores", anomaly_detection_results["anomaly_detection"])

    plot_anomaly_detection_results(
        data=dataset_for_ANOD_testing["X"],
        true_anomalies=dataset_for_ANOD_testing["anomaly_y"],
        predicted_anomalies=anomaly_detection_results["anomaly_detection"],
        anomaly_scores=anomaly_scores,
        metrics=metrics_dict,
        model_name="PatchTST"
    )

    print("PatchTST anomaly detection example completed successfully!")

def run_demo_plots():
    """Generate demo plots with synthetic data for quick visualization demonstration."""
    print_separator("📊 Demo Visualization Plots")
    print("Generating demonstration plots with synthetic data...")

    # Setup plotting
    setup_plotting()

    # Generate synthetic data for demonstrations
    np.random.seed(42)
    n_samples, n_steps, n_features = 100, 48, 5

    # 1. Demo Imputation Plot
    print("Generating imputation demo plot...")
    original_data = np.random.randn(n_samples, n_steps, n_features).cumsum(axis=1)
    missing_mask = np.random.random((n_samples, n_steps, n_features)) < 0.2
    imputed_data = original_data.copy()
    imputed_data[missing_mask] = np.random.randn(missing_mask.sum()) * 0.5
    ground_truth = original_data.copy()

    plot_imputation_results(
        original_data=original_data[:, :, 0],
        imputed_data=imputed_data[:, :, 0],
        missing_mask=missing_mask[:, :, 0],
        ground_truth=ground_truth[:, :, 0],
        model_name="Demo_Imputation",
        mae_score=0.1234
    )

    # 2. Demo Forecasting Plot
    print("Generating forecasting demo plot...")
    historical_data = np.random.randn(n_samples, n_steps-6, n_features).cumsum(axis=1)
    true_future = np.random.randn(n_samples, 6, n_features).cumsum(axis=1)
    predictions = true_future + np.random.randn(*true_future.shape) * 0.3

    plot_forecasting_results(
        historical_data=historical_data[:, :, 0],
        true_future=true_future[:, :, 0],
        predictions=predictions[:, :, 0],
        model_name="Demo_Forecasting",
        mae_score=0.2345
    )

    # 3. Demo Classification Plot
    print("Generating classification demo plot...")
    class_data = np.random.randn(n_samples, n_steps, n_features).cumsum(axis=1)
    true_labels = np.random.randint(0, 2, n_samples)
    predicted_probs = np.random.random(n_samples)

    # Create synthetic metrics
    demo_metrics = {
        "roc_auc": 0.85,
        "pr_auc": 0.78,
        "f1": 0.72,
        "precision": 0.75,
        "recall": 0.70
    }

    plot_classification_results(
        data=class_data[:, :, 0],
        true_labels=true_labels,
        predicted_probs=predicted_probs,
        metrics=demo_metrics,
        model_name="Demo_Classification"
    )

    # 4. Demo Clustering Plot
    print("Generating clustering demo plot...")
    cluster_data = np.random.randn(n_samples, n_steps, n_features).cumsum(axis=1)
    cluster_labels = np.random.randint(0, 3, n_samples)
    true_cluster_labels = np.random.randint(0, 3, n_samples)

    demo_cluster_metrics = {"RI": 0.65, "CP": 0.72}

    plot_clustering_results(
        data=cluster_data[:, :, 0],
        cluster_labels=cluster_labels,
        true_labels=true_cluster_labels,
        metrics=demo_cluster_metrics,
        model_name="Demo_Clustering"
    )

    # 5. Demo Anomaly Detection Plot
    print("Generating anomaly detection demo plot...")
    anomaly_data = np.random.randn(n_samples, n_steps, n_features).cumsum(axis=1)
    true_anomalies = np.random.random((n_samples, n_steps, n_features)) < 0.05
    predicted_anomalies = np.random.random((n_samples, n_steps, n_features)) < 0.07
    anomaly_scores = np.random.random((n_samples, n_steps, n_features))

    demo_anomaly_metrics = {
        "accuracy": 0.92,
        "f1": 0.68,
        "precision": 0.75,
        "recall": 0.62
    }

    plot_anomaly_detection_results(
        data=anomaly_data[:, :, 0],
        true_anomalies=true_anomalies[:, :, 0],
        predicted_anomalies=predicted_anomalies[:, :, 0],
        anomaly_scores=anomaly_scores[:, :, 0],
        metrics=demo_anomaly_metrics,
        model_name="Demo_Anomaly_Detection"
    )

    print_separator("✅ Demo Plots Generated Successfully!")
    print("All demonstration plots have been saved to the 'plots/' directory.")
    print("These plots show the types of visualizations generated for each model type.")

def run_all_examples():
    """Run all examples sequentially."""
    print_separator("🚀 Running All PyPOTS Examples")

    examples = [
        ("Data Preparation", prepare_physionet2012_dataset),
        ("SAITS Imputation", run_saits_imputation),
        ("CSDI Imputation", run_csdi_imputation),
        ("TEFN Forecasting", run_tefn_forecasting),
        ("TimeMixer++ Forecasting", run_timemixer_forecasting),
        ("TimesNet Classification", run_timesnet_classification),
        ("BRITS Classification", run_brits_classification),
        ("CRLI Clustering", run_crli_clustering),
        ("VaDER Clustering", run_vader_clustering),
        ("Autoformer Anomaly Detection", run_autoformer_anomaly_detection),
        ("PatchTST Anomaly Detection", run_patchtst_anomaly_detection),
    ]

    for i, (name, func) in enumerate(examples, 1):
        print(f"\n[{i}/{len(examples)}] Running {name}...")
        try:
            if name == "Data Preparation":
                func()  # Just prepare data, don't run a full example
                print("Data preparation completed!")
            else:
                func()
        except Exception as e:
            print(f"❌ Error in {name}: {str(e)}")
            print("Continuing with next example...")

    print_separator("🎉 All Examples Completed!")

def display_menu():
    """Display the interactive menu."""
    print("\n" + "="*80)
    print("  🌟 PyPOTS Interactive Tutorial Menu")
    print("="*80)
    print()
    print("📊 IMPUTATION MODELS:")
    print("  1. SAITS Imputation Example")
    print("  2. CSDI Imputation Example")
    print()
    print("📈 FORECASTING MODELS:")
    print("  3. TEFN Forecasting Example")
    print("  4. TimeMixer++ Forecasting Example")
    print()
    print("🏷️  CLASSIFICATION MODELS:")
    print("  5. TimesNet Classification Example")
    print("  6. BRITS Classification Example")
    print()
    print("🔍 CLUSTERING MODELS:")
    print("  7. CRLI Clustering Example")
    print("  8. VaDER Clustering Example")
    print()
    print("🚨 ANOMALY DETECTION MODELS:")
    print("  9. Autoformer Anomaly Detection Example")
    print("  10. PatchTST Anomaly Detection Example")
    print()
    print("🎯 SPECIAL OPTIONS:")
    print("  11. Run All Examples Sequentially")
    print("  12. Generate Demo Visualization Plots (Quick Preview)")
    print("  0. Exit")
    print()
    print("="*80)

def get_user_choice():
    """Get and validate user input."""
    while True:
        try:
            choice = input("Enter your choice (0-12): ").strip()
            if choice.isdigit():
                choice_num = int(choice)
                if 0 <= choice_num <= 12:
                    return choice_num
            print("❌ Invalid input! Please enter a number between 0 and 12.")
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Error: {e}. Please try again.")

def main():
    """Main interactive menu function."""
    print_separator("🤗 Welcome to PyPOTS Interactive Tutorial!")
    print("This tutorial demonstrates various PyPOTS models for time series analysis.")
    print("You can run individual examples or all examples sequentially.")
    print("Each example is self-contained and can run independently.")

    # Menu options mapping
    menu_options = {
        1: ("SAITS Imputation", run_saits_imputation),
        2: ("CSDI Imputation", run_csdi_imputation),
        3: ("TEFN Forecasting", run_tefn_forecasting),
        4: ("TimeMixer++ Forecasting", run_timemixer_forecasting),
        5: ("TimesNet Classification", run_timesnet_classification),
        6: ("BRITS Classification", run_brits_classification),
        7: ("CRLI Clustering", run_crli_clustering),
        8: ("VaDER Clustering", run_vader_clustering),
        9: ("Autoformer Anomaly Detection", run_autoformer_anomaly_detection),
        10: ("PatchTST Anomaly Detection", run_patchtst_anomaly_detection),
        11: ("All Examples", run_all_examples),
        12: ("Demo Visualization Plots", run_demo_plots),
    }

    while True:
        display_menu()
        choice = get_user_choice()

        if choice == 0:
            print_separator("👋 Thank you for using PyPOTS Interactive Tutorial!")
            print("Happy time series modeling! 🚀")
            break

        if choice in menu_options:
            name, func = menu_options[choice]
            print(f"\n🚀 Starting {name}...")

            try:
                func()
                print(f"\n✅ {name} completed successfully!")
                input("\nPress Enter to return to the main menu...")
            except KeyboardInterrupt:
                print(f"\n\n⚠️  {name} was interrupted by user.")
                input("Press Enter to return to the main menu...")
            except Exception as e:
                print(f"\n❌ Error in {name}: {str(e)}")
                print("Please check your environment and try again.")
                input("Press Enter to return to the main menu...")
        else:
            print("❌ Invalid choice! Please try again.")

if __name__ == "__main__":
    main()
